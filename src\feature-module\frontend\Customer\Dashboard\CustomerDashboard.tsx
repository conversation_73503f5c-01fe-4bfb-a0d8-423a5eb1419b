import { FaS<PERSON><PERSON><PERSON>art, <PERSON><PERSON><PERSON><PERSON>, FaUser } from 'react-icons/fa';
import BreadCrumb from '../../../components/common/breadcrumb/breadCrumb';
import CustomButton from '../../../components/CustomButton';
import { Chip } from '@heroui/react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useAuth } from 'react-oidc-context';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { getUserBookings, cancelBooking, Booking as BookingType } from '../../../../service/bookingService';
import { all_routes } from '../../../../core/data/routes/all_routes';

export default function Dashboard() {
  const navigate = useNavigate();
  const [cancellingId, setCancellingId] = useState<string | number | null>(null);

  const handleViewAll = useCallback(() => {
    navigate(all_routes.booking2);
  }, [navigate]);


  // Booking data (from API)
  const auth = useAuth();
  const [bookingItems, setBookingItems] = useState<BookingType[]>([]);
  const [bookingsLoading, setBookingsLoading] = useState(false);
  const [bookingsError, setBookingsError] = useState<string | null>(null);

  // Get user ID from auth context
  const getUserId = useCallback(() => {
    if (auth.user && auth.user.profile) {
      const profile = auth.user.profile as {
        preferred_username?: string;
        sub?: string;
        email?: string;
      };
      return profile.preferred_username || profile.sub || profile.email || null;
    }
    return null;
  }, [auth.user]);

  // Fetch recent bookings
  const fetchBookings = useCallback(async () => {
    const uid = getUserId();
    if (!uid || !auth.isAuthenticated) return;

    try {
      setBookingsLoading(true);
      setBookingsError(null);
      const response = await getUserBookings({ userId: uid, page: 1, limit: 6 });
      if (response && response.bookings) {
        setBookingItems(response.bookings);
      } else {
        setBookingItems([]);
      }
    } catch (error) {
      console.error('Failed to load bookings on dashboard', error);
      setBookingsError('Failed to load bookings');
      setBookingItems([]);
    } finally {
      setBookingsLoading(false);
    }
  }, [auth.isAuthenticated, getUserId]);

  useEffect(() => {
    if (auth.isAuthenticated && !auth.isLoading) {
      fetchBookings();
    }
  }, [auth.isAuthenticated, auth.isLoading, fetchBookings]);

  // Derive recent transactions from bookings
  const recentTransactions = useMemo(() => {
    return bookingItems.slice(0, 4).map((b) => ({
      type: b.serviceName || b.service || 'Service Booking',
      date: b.date || b.appointmentDate || '',
      amount: b.amount || '',
      status: (b.status as string) || 'Pending',
    }));
  }, [bookingItems]);

  // Cancel a booking and refresh
  const handleCancelBooking = useCallback(async (id: string | number) => {
    try {
      setCancellingId(id);
      await cancelBooking(String(id));
      toast.success('Booking cancelled');
      await fetchBookings();
    } catch {
      toast.error('Failed to cancel booking');
    } finally {
      setCancellingId(null);
    }
  }, [fetchBookings]);

  // Get color classes for status
  type ChipColor = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  const getStatusColor = (status: string): ChipColor => {
    switch (status) {
      case 'Completed':
      case 'Finished':
        return 'success';
      case 'Pending':
      case 'Inprogress':
      case 'Confirmed':
        return 'primary';
      case 'Rescheduled':
        return 'warning';
      case 'Cancelled':
        return 'danger';
      default:
        return 'default';
    }
  };

  // StatusChip component for consistent status display
  const StatusChip = ({ status }: { status: string }) => (
    <Chip
      color={getStatusColor(status)}
      variant="flat"
      size="sm"
      radius="sm"
      className="font-medium"
    >
      {status}
    </Chip>
  );

  // Metrics derived from real bookings
  const metrics = useMemo(() => {
    const totalOrders = bookingItems.length;
    const totalCompleted = bookingItems.filter((b) => b.status === 'Completed' || b.status === 'Finished').length;
    const totalCancelled = bookingItems.filter((b) => b.status === 'Cancelled').length;
    const totalSpendNumber = bookingItems.reduce((sum, b) => {
      const n = parseFloat(String(b.amount || '').replace(/[^0-9.]/g, ''));
      return sum + (isNaN(n) ? 0 : n);
    }, 0);
    const firstAmount = bookingItems.find((b) => b.amount)?.amount || '';
    const currencyMatch = String(firstAmount).match(/[^0-9\s.,-]/);
    const currencySymbol = currencyMatch ? currencyMatch[0] : '$';
    const totalSpendFormatted = `${currencySymbol}${totalSpendNumber.toFixed(2)}`;
    return { totalOrders, totalCompleted, totalCancelled, totalSpendFormatted };
  }, [bookingItems]);

  return (
    <div className="max-w-screen-xl mx-auto p-3 sm:p-4 md:p-6 w-full">
      {/* Breadcrumb & Title */}
      <BreadCrumb title="Dashboard" item1="Customer" />
      <h2 className="text-2xl font-semibold text-gray-800 mt-4 mb-6">Dashboard</h2>

      {/* Total Orders */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8">

        {/* Real totals based on bookings */}
        {[
          { title: 'Total Orders', value: metrics.totalOrders },
          { title: 'Total Spend', value: metrics.totalSpendFormatted },
          { title: 'Completed', value: metrics.totalCompleted },
          { title: 'Cancelled', value: metrics.totalCancelled },
        ].map((stat, index) => (
          <div
            key={index}
            className="bg-white p-4 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-lg flex items-center justify-between"
          >
            <div className="p-3 rounded-full bg-gray-100 text-lg flex items-center justify-center text-gray-600">
              <FaShoppingCart />
            </div>
            <div className="flex-1 ml-4">
              <p className="text-gray-500 text-sm font-medium">{stat.title}</p>
              <p className="text-xl font-semibold">{stat.value}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Transactions & Bookings Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Transactions */}
        <div className="bg-white p-5 md:p-6 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-lg">
          <div className="flex justify-between items-center mb-5">
            <h3 className="font-bold text-lg text-gray-800">Recent Transactions</h3>
            <CustomButton
              label="View All"
              color="primary"
              variant="light"
              size="sm"
              endContent={<FaEye />}
              onPress={handleViewAll}
            />
          </div>
          <div className="space-y-3">
            {recentTransactions.map((transaction, index) => (
              <div
                key={index}
                className="flex justify-between items-center bg-gray-50 hover:bg-gray-100 transition-colors duration-200 p-4 rounded-lg border border-gray-100"
              >
                <div className="flex-1">
                  <h4 className="text-base md:text-lg font-medium">{transaction.type}</h4>
                  <p className="text-sm text-gray-500">
                    {transaction.date}
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <StatusChip status={transaction.status} />
                  <span className="font-semibold text-gray-700 text-lg">
                    {transaction.amount}
                  </span>
                </div>
              </div>
            ))}
          </div>
          {bookingsLoading && (
            <div className="text-center py-6 text-gray-500">Loading transactions...</div>
          )}
          {!bookingsLoading && recentTransactions.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>No recent transactions found</p>
            </div>
          )}
        </div>

        {/* Recent Bookings */}
        <div className="bg-white p-5 md:p-6 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-lg">
          <div className="flex justify-between items-center mb-5">
            <h3 className="text-lg font-bold text-gray-800">Recent Bookings</h3>
            <CustomButton
              label="View All"
              color="primary"
              variant="light"
              size="sm"
              endContent={<FaEye />}
              onPress={handleViewAll}
            />
          </div>
          <div className="space-y-3">
            {bookingItems.slice(0, 4).map((booking, index) => (
              <div
                key={index}
                className="flex flex-col sm:flex-row items-start sm:items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors duration-200 p-4 rounded-lg border border-gray-100"
              >
                <div className="flex items-center space-x-3 md:space-x-4 flex-1">
                  <div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                    <FaUser className="text-blue-600 text-xl" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-base md:text-lg font-medium">{booking.serviceName || booking.service}</h4>
                    <p className="text-sm text-gray-500">
                      {booking.date || booking.appointmentDate}
                    </p>
                    <p className="text-sm text-gray-600 font-medium">{booking.provider}</p>
                    <p className="text-xs text-gray-500">{booking.email}</p>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 w-full sm:w-auto mt-3 sm:mt-0">
                  <StatusChip status={booking.status} />
                  {booking.status !== 'Completed' && booking.status !== 'Cancelled' && (
                    <CustomButton
                      color="danger"
                      label={cancellingId === booking.id ? 'Cancelling...' : 'Cancel'}
                      size="sm"
                      variant="light"
                      isDisabled={!!cancellingId}
                      onPress={() => handleCancelBooking(booking.id)}
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
          {bookingsLoading && (
            <div className="text-center py-6 text-gray-500">Loading bookings...</div>
          )}
          {!bookingsLoading && bookingItems.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>No recent bookings found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
